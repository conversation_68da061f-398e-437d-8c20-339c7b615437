package mgi.tools.dumpers;

import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;

/**
 * Utility to dump/pack if3 files (whole directory or single file)
 * Note: If3 files are handled as raw binary data, not as structured definitions
 */
public class If3PackTool {

    // ----------------- CONFIGURE -----------------
    private static final String IF3_PACK_DIR = "cache/assets/osnr/store_interface/if3";   // where .if3.pack files live
    private static final String BINARY_DIR = "dumps/if3";           // Directory for binary files

    private static final boolean DUMP_MODE = true;                // true=dump pack->binary, false=binary->pack

    // Single-file import (optional)
    private static final boolean IMPORT_SINGLE_FILE = false;      // true=import this single file
    private static final String SINGLE_BINARY_FILE = "dumps/if3/0.if3";  // Path to binary file
    private static final String SINGLE_PACK_OUTPUT = "cache/assets/osnr/store_interface/if3/0"; // Path to output .if3.pack
    // ----------------------------------------------

    public static void main(String[] args) throws Exception {
        if (IMPORT_SINGLE_FILE) {
            importSingleFile();
        } else {
            if (DUMP_MODE) {
                dumpAll();
            } else {
                importAll();
            }
        }
    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(IF3_PACK_DIR);
        File outDir = new File(BINARY_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".if3.pack"))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());

            String baseName = f.getName().replace(".if3.pack", ".if3");
            File outFile = new File(outDir, baseName);

            Files.write(outFile.toPath(), raw);
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    private static void importAll() throws Exception {
        File inDir = new File(BINARY_DIR);
        File outDir = new File(IF3_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".if3"))) {
            System.out.println("Importing " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());

            String baseName = f.getName().replace(".if3", ".if3.pack");
            File outFile = new File(outDir, baseName);
            Files.write(outFile.toPath(), raw);
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        File inFile = new File(SINGLE_BINARY_FILE);
        File outFile = new File(SINGLE_PACK_OUTPUT);

        System.out.println("Importing single file: " + inFile);

        byte[] raw = Files.readAllBytes(inFile.toPath());
        Files.write(outFile.toPath(), raw);

        System.out.println("✅ Single file written to: " + outFile);
    }
}
