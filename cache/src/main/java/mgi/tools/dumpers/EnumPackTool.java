package mgi.tools.dumpers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import mgi.types.config.enums.EnumDefinitions;
import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;

/**
 * Utility to dump/pack enums (whole directory or single file)
 */
public class EnumPackTool {

    // ----------------- CONFIGURE -----------------
    private static final String ENUM_PACK_DIR = "cache/assets/enum";   // where .enum.pack files live
    private static final String JSON_DIR = "dumps/enum";           // Directory for JSON files

    private static final boolean DUMP_MODE = false;                // true=dump pack->json, false=json->pack

    // Single-file import (optional)
    private static final boolean IMPORT_SINGLE_FILE = true;      // true=import this single file
    private static final String SINGLE_JSON_FILE = "dumps/enum/10039.json";  // Path to JSON
    private static final String SINGLE_PACK_OUTPUT = "cache/assets/enum/10039"; // Path to output .enum.pack
    // ----------------------------------------------

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public static void main(String[] args) throws Exception {
        if (IMPORT_SINGLE_FILE) {
            importSingleFile();
        } else {
            if (DUMP_MODE) {
                dumpAll();
            } else {
                importAll();
            }
        }
    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(ENUM_PACK_DIR);
        File outDir = new File(JSON_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".enum.pack"))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());
            ByteBuffer buf = new ByteBuffer(raw);

            EnumDefinitions defs = new EnumDefinitions();
            defs.decode(buf);

            String baseName = f.getName().replace(".enum.pack", ".json");
            File outFile = new File(outDir, baseName);

            try (FileWriter writer = new FileWriter(outFile)) {
                gson.toJson(defs, writer);
            }
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    private static void importAll() throws Exception {
        File inDir = new File(JSON_DIR);
        File outDir = new File(ENUM_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".json"))) {
            System.out.println("Importing " + f.getName());
            EnumDefinitions defs;
            try (FileReader reader = new FileReader(f)) {
                defs = gson.fromJson(reader, EnumDefinitions.class);
            }

            ByteBuffer buf = defs.encode();
            byte[] data = buf.toArray(0, buf.getPosition());

            String baseName = f.getName().replace(".json", ".enum.pack");
            File outFile = new File(outDir, baseName);
            Files.write(outFile.toPath(), data);
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        File inFile = new File(SINGLE_JSON_FILE);
        File outFile = new File(SINGLE_PACK_OUTPUT);

        System.out.println("Importing single file: " + inFile);

        EnumDefinitions defs;
        try (FileReader reader = new FileReader(inFile)) {
            defs = gson.fromJson(reader, EnumDefinitions.class);
        }

        ByteBuffer buf = defs.encode();
        byte[] data = buf.toArray(0, buf.getPosition());

        Files.write(outFile.toPath(), data);

        System.out.println("✅ Single file written to: " + outFile);
    }
}