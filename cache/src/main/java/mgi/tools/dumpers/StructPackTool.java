package mgi.tools.dumpers;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import mgi.types.config.StructDefinitions;
import mgi.utilities.ByteBuffer;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.nio.file.Files;

/**
 * Utility to dump/pack structs (whole directory or single file)
 */
public class StructPackTool {

    // ----------------- CONFIGURE -----------------
    private static final String STRUCT_PACK_DIR = "cache/assets/structs";   // where .struct.pack files live
    private static final String JSON_DIR = "dumps/struct";           // Directory for JSON files

    private static final boolean DUMP_MODE = false;                // true=dump pack->json, false=json->pack

    // Single-file import (optional)
    private static final boolean IMPORT_SINGLE_FILE = true;      // true=import this single file
    private static final String SINGLE_JSON_FILE = "dumps/struct/500.json";  // Path to JSON
    private static final String SINGLE_PACK_OUTPUT = "cache/assets/structs/500"; // Path to output .struct.pack
    // ----------------------------------------------

    private static final Gson gson = new GsonBuilder().setPrettyPrinting().create();

    public static void main(String[] args) throws Exception {
        if (IMPORT_SINGLE_FILE) {
            importSingleFile();
        } else {
            if (DUMP_MODE) {
                dumpAll();
            } else {
                importAll();
            }
        }
    }

    // ----------------- DIRECTORY FUNCTIONS -----------------
    private static void dumpAll() throws Exception {
        File inDir = new File(STRUCT_PACK_DIR);
        File outDir = new File(JSON_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".struct.pack"))) {
            System.out.println("Dumping " + f.getName());
            byte[] raw = Files.readAllBytes(f.toPath());
            ByteBuffer buf = new ByteBuffer(raw);

            // Create a new StructDefinitions instance with the ID from filename
            String baseName = f.getName().replace(".struct.pack", "");
            int id = Integer.parseInt(baseName);
            StructDefinitions defs = new StructDefinitions(id);
            defs.decode(buf);

            String jsonName = baseName + ".json";
            File outFile = new File(outDir, jsonName);

            try (FileWriter writer = new FileWriter(outFile)) {
                gson.toJson(defs, writer);
            }
        }
        System.out.println("✅ Dump complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    private static void importAll() throws Exception {
        File inDir = new File(JSON_DIR);
        File outDir = new File(STRUCT_PACK_DIR);
        if (!outDir.exists()) outDir.mkdirs();

        for (File f : inDir.listFiles((dir, name) -> name.endsWith(".json"))) {
            System.out.println("Importing " + f.getName());
            StructDefinitions defs;
            try (FileReader reader = new FileReader(f)) {
                defs = gson.fromJson(reader, StructDefinitions.class);
            }

            ByteBuffer buf = defs.encode();
            byte[] data = buf.toArray(0, buf.getPosition());

            String baseName = f.getName().replace(".json", ".struct.pack");
            File outFile = new File(outDir, baseName);
            Files.write(outFile.toPath(), data);
        }
        System.out.println("✅ Import complete: " + inDir.getAbsolutePath() + " → " + outDir.getAbsolutePath());
    }

    // ----------------- SINGLE FILE FUNCTION -----------------
    private static void importSingleFile() throws Exception {
        File inFile = new File(SINGLE_JSON_FILE);
        File outFile = new File(SINGLE_PACK_OUTPUT);

        System.out.println("Importing single file: " + inFile);

        StructDefinitions defs;
        try (FileReader reader = new FileReader(inFile)) {
            defs = gson.fromJson(reader, StructDefinitions.class);
        }

        ByteBuffer buf = defs.encode();
        byte[] data = buf.toArray(0, buf.getPosition());

        Files.write(outFile.toPath(), data);

        System.out.println("✅ Single file written to: " + outFile);
    }
}
